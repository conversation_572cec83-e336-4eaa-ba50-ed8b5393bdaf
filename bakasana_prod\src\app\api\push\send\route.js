import { NextResponse } from 'next/server';
import { subscriptions } from '../subscribe/route.js';

export async function POST(request) {
  try {
    const notificationData = await request.json();

    // Validate notification data
    if (!notificationData.title || !notificationData.body) {
      return NextResponse.json(
        { error: 'Title and body are required' },
        { status: 400 }
      );
    }

    // Get active subscriptions
    const activeSubscriptions = Array.from(subscriptions.values())
      .filter(sub => sub.isActive);

    if (activeSubscriptions.length === 0) {
      return NextResponse.json(
        { error: 'No active subscriptions found' },
        { status: 404 }
      );
    }

    // Send notifications to all active subscriptions
    const results = await sendNotificationToSubscriptions(activeSubscriptions, notificationData);

    // Count successful and failed sends
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    console.log(`Notifications sent: ${successful} successful, ${failed} failed`);

    return NextResponse.json({
      success: true,
      totalSent: successful,
      totalFailed: failed,
      totalSubscriptions: activeSubscriptions.length,
      results: process.env.NODE_ENV === 'development' ? results : undefined
    });

  } catch (error) {
    console.error('Push notification send error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to send notifications',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

// Send notification to multiple subscriptions
async function sendNotificationToSubscriptions(subscriptions, notificationData) {
  const webpush = await import('web-push');
  
  // Configure web-push
  webpush.setVapidDetails(
    'mailto:<EMAIL>',
    process.env.VAPID_PUBLIC_KEY,
    process.env.VAPID_PRIVATE_KEY
  );

  // Prepare notification payload
  const payload = JSON.stringify({
    title: notificationData.title,
    body: notificationData.body,
    icon: notificationData.icon || '/apple-touch-icon.png',
    badge: notificationData.badge || '/favicon-32x32.png',
    image: notificationData.image,
    data: {
      url: notificationData.data?.url || '/',
      type: notificationData.data?.type || 'general',
      timestamp: new Date().toISOString(),
      ...notificationData.data
    },
    actions: notificationData.actions || [
      {
        action: 'view',
        title: 'Zobacz szczegóły',
        icon: '/favicon-16x16.png'
      },
      {
        action: 'dismiss',
        title: 'Zamknij',
        icon: '/favicon-16x16.png'
      }
    ],
    requireInteraction: notificationData.requireInteraction || false,
    silent: notificationData.silent || false,
    tag: notificationData.tag || 'bakasana-notification',
    renotify: notificationData.renotify || false
  });

  // Send to all subscriptions in parallel
  const sendPromises = subscriptions.map(async (subscriptionData) => {
    try {
      await webpush.sendNotification(subscriptionData.subscription, payload);
      return {
        subscriptionId: subscriptionData.id,
        success: true
      };
    } catch (error) {
      console.error(`Failed to send to subscription ${subscriptionData.id}:`, error);
      
      // Handle specific errors
      if (error.statusCode === 410 || error.statusCode === 404) {
        // Subscription is no longer valid, mark as inactive
        subscriptionData.isActive = false;
        subscriptionData.invalidatedAt = new Date().toISOString();
        console.log(`Marked subscription ${subscriptionData.id} as inactive`);
      }
      
      return {
        subscriptionId: subscriptionData.id,
        success: false,
        error: error.message,
        statusCode: error.statusCode
      };
    }
  });

  return await Promise.all(sendPromises);
}

// GET endpoint for testing (development only)
export async function GET(request) {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Not available in production' },
      { status: 403 }
    );
  }

  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type') || 'test';

  let notificationData;

  switch (type) {
    case 'retreat':
      notificationData = {
        title: 'Nowy retreat dostępny! 🌴',
        body: 'Bali Yoga Retreat 2025 - Odkryj magię jogi na rajskiej wyspie',
        image: '/images/bali-retreat.jpg',
        data: {
          url: '/retreaty/bali-2025',
          type: 'new_retreat',
          retreatId: 'bali-2025'
        }
      };
      break;
    
    case 'blog':
      notificationData = {
        title: 'Nowy wpis na blogu 📝',
        body: '5 porad jak przygotować się do retreatu jogi',
        image: '/images/blog-post.jpg',
        data: {
          url: '/blog/przygotowanie-do-retreatu',
          type: 'blog_post',
          postId: 'przygotowanie-do-retreatu'
        }
      };
      break;
    
    default:
      notificationData = {
        title: 'Test powiadomienia BAKASANA 🧘‍♀️',
        body: 'To jest testowe powiadomienie push. Wszystko działa poprawnie!',
        data: {
          url: '/',
          type: 'test'
        }
      };
  }

  // Send the test notification
  try {
    const response = await fetch(new URL('/api/push/send', request.url), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(notificationData),
    });

    const result = await response.json();
    
    return NextResponse.json({
      message: 'Test notification sent',
      type: type,
      notificationData: notificationData,
      result: result
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to send test notification', details: error.message },
      { status: 500 }
    );
  }
}
