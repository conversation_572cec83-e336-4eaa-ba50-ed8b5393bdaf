// Push Notifications Service for BAKASANA PWA
// Handles subscription, sending, and managing push notifications

class PushNotificationService {
  constructor() {
    this.vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;
    this.isSupported = this.checkSupport();
  }

  // Check if push notifications are supported
  checkSupport() {
    return (
      'serviceWorker' in navigator &&
      'PushManager' in window &&
      'Notification' in window
    );
  }

  // Convert VAPID key to Uint8Array
  urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Request notification permission
  async requestPermission() {
    if (!this.isSupported) {
      throw new Error('Push notifications not supported');
    }

    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      console.log('Notification permission granted');
      return true;
    } else {
      console.log('Notification permission denied');
      return false;
    }
  }

  // Subscribe to push notifications
  async subscribe() {
    if (!this.isSupported) {
      throw new Error('Push notifications not supported');
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      
      // Check if already subscribed
      const existingSubscription = await registration.pushManager.getSubscription();
      if (existingSubscription) {
        console.log('Already subscribed to push notifications');
        return existingSubscription;
      }

      // Subscribe to push notifications
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
      });

      console.log('Push notification subscription created:', subscription);

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);

      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      throw error;
    }
  }

  // Unsubscribe from push notifications
  async unsubscribe() {
    if (!this.isSupported) {
      throw new Error('Push notifications not supported');
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();

      if (subscription) {
        await subscription.unsubscribe();
        console.log('Unsubscribed from push notifications');
        
        // Remove subscription from server
        await this.removeSubscriptionFromServer(subscription);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      throw error;
    }
  }

  // Send subscription to server
  async sendSubscriptionToServer(subscription) {
    try {
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription: subscription,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send subscription to server');
      }

      console.log('Subscription sent to server successfully');
    } catch (error) {
      console.error('Error sending subscription to server:', error);
      throw error;
    }
  }

  // Remove subscription from server
  async removeSubscriptionFromServer(subscription) {
    try {
      const response = await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription: subscription
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to remove subscription from server');
      }

      console.log('Subscription removed from server successfully');
    } catch (error) {
      console.error('Error removing subscription from server:', error);
      throw error;
    }
  }

  // Get current subscription status
  async getSubscriptionStatus() {
    if (!this.isSupported) {
      return { supported: false, subscribed: false, permission: 'unsupported' };
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();
      
      return {
        supported: true,
        subscribed: !!subscription,
        permission: Notification.permission,
        subscription: subscription
      };
    } catch (error) {
      console.error('Error getting subscription status:', error);
      return { supported: true, subscribed: false, permission: 'default' };
    }
  }

  // Send test notification (for development)
  async sendTestNotification() {
    try {
      const response = await fetch('/api/push/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to send test notification');
      }

      console.log('Test notification sent');
    } catch (error) {
      console.error('Error sending test notification:', error);
      throw error;
    }
  }

  // Show local notification (fallback)
  showLocalNotification(title, options = {}) {
    if (!this.isSupported) {
      console.warn('Notifications not supported');
      return;
    }

    if (Notification.permission === 'granted') {
      const notification = new Notification(title, {
        icon: '/apple-touch-icon.png',
        badge: '/favicon-32x32.png',
        ...options
      });

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      return notification;
    }
  }

  // Retreat-specific notification methods
  async notifyNewRetreat(retreatData) {
    return this.sendNotification({
      title: 'Nowy retreat dostępny!',
      body: `${retreatData.name} - ${retreatData.destination}`,
      icon: '/apple-touch-icon.png',
      image: retreatData.image,
      data: {
        url: `/retreaty/${retreatData.slug}`,
        type: 'new_retreat',
        retreatId: retreatData.id
      },
      actions: [
        {
          action: 'view',
          title: 'Zobacz szczegóły'
        },
        {
          action: 'dismiss',
          title: 'Zamknij'
        }
      ]
    });
  }

  async notifyBookingReminder(bookingData) {
    return this.sendNotification({
      title: 'Przypomnienie o rezeracji',
      body: `Twój retreat ${bookingData.retreatName} rozpoczyna się za ${bookingData.daysUntil} dni`,
      icon: '/apple-touch-icon.png',
      data: {
        url: '/moje-rezerwacje',
        type: 'booking_reminder',
        bookingId: bookingData.id
      }
    });
  }

  async notifyBlogPost(postData) {
    return this.sendNotification({
      title: 'Nowy wpis na blogu',
      body: postData.title,
      icon: '/apple-touch-icon.png',
      image: postData.featuredImage,
      data: {
        url: `/blog/${postData.slug}`,
        type: 'blog_post',
        postId: postData.id
      }
    });
  }

  // Generic notification sender
  async sendNotification(notificationData) {
    try {
      const response = await fetch('/api/push/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationData),
      });

      if (!response.ok) {
        throw new Error('Failed to send notification');
      }

      console.log('Notification sent successfully');
      return true;
    } catch (error) {
      console.error('Error sending notification:', error);
      
      // Fallback to local notification
      this.showLocalNotification(notificationData.title, {
        body: notificationData.body,
        icon: notificationData.icon,
        image: notificationData.image
      });
      
      return false;
    }
  }
}

// Create singleton instance
const pushNotificationService = new PushNotificationService();

// React hook for push notifications
export function usePushNotifications() {
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [permission, setPermission] = useState('default');

  useEffect(() => {
    const checkStatus = async () => {
      const status = await pushNotificationService.getSubscriptionStatus();
      setIsSupported(status.supported);
      setIsSubscribed(status.subscribed);
      setPermission(status.permission);
    };

    checkStatus();
  }, []);

  const subscribe = async () => {
    try {
      const hasPermission = await pushNotificationService.requestPermission();
      if (hasPermission) {
        await pushNotificationService.subscribe();
        setIsSubscribed(true);
        setPermission('granted');
      }
    } catch (error) {
      console.error('Subscription failed:', error);
    }
  };

  const unsubscribe = async () => {
    try {
      await pushNotificationService.unsubscribe();
      setIsSubscribed(false);
    } catch (error) {
      console.error('Unsubscription failed:', error);
    }
  };

  return {
    isSupported,
    isSubscribed,
    permission,
    subscribe,
    unsubscribe,
    sendTestNotification: pushNotificationService.sendTestNotification.bind(pushNotificationService)
  };
}

export default pushNotificationService;
