'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { UnifiedButton } from '@/components/ui/UnifiedButton';

export default function PWAManager() {
  const [isInstalled, setIsInstalled] = useState(false);
  const [isInstallable, setIsInstallable] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showInstallBanner, setShowInstallBanner] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [notificationPermission, setNotificationPermission] = useState('default');

  // Check if app is installed
  const checkInstallStatus = useCallback(() => {
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isIOSStandalone = window.navigator.standalone === true;
    const isInstalled = isStandalone || isIOSStandalone;
    
    setIsInstalled(isInstalled);
    
    // Don't show install banner if already installed
    if (isInstalled) {
      setShowInstallBanner(false);
    }
  }, []);

  // Register service worker and handle updates
  const registerServiceWorker = useCallback(async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw-enhanced.js');
        console.log('Enhanced SW registered:', registration);

        // Check for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setUpdateAvailable(true);
            }
          });
        });

        // Listen for messages from SW
        navigator.serviceWorker.addEventListener('message', (event) => {
          if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
            setUpdateAvailable(true);
          }
        });

      } catch (error) {
        console.error('SW registration failed:', error);
      }
    }
  }, []);

  // Handle install prompt
  const handleInstallPrompt = useCallback((e) => {
    e.preventDefault();
    setDeferredPrompt(e);
    setIsInstallable(true);
    
    // Show install banner after user interaction
    setTimeout(() => {
      if (!isInstalled && !localStorage.getItem('pwa-install-dismissed')) {
        setShowInstallBanner(true);
      }
    }, 10000); // Show after 10 seconds
  }, [isInstalled]);

  // Handle online/offline status
  const handleOnlineStatus = useCallback(() => {
    setIsOnline(navigator.onLine);
  }, []);

  // Check notification permission
  const checkNotificationPermission = useCallback(() => {
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission);
    }
  }, []);

  // Initialize PWA features
  useEffect(() => {
    checkInstallStatus();
    registerServiceWorker();
    checkNotificationPermission();

    // Event listeners
    window.addEventListener('beforeinstallprompt', handleInstallPrompt);
    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOnlineStatus);
    
    // Check initial online status
    handleOnlineStatus();

    return () => {
      window.removeEventListener('beforeinstallprompt', handleInstallPrompt);
      window.removeEventListener('online', handleOnlineStatus);
      window.removeEventListener('offline', handleOnlineStatus);
    };
  }, [checkInstallStatus, registerServiceWorker, handleInstallPrompt, handleOnlineStatus, checkNotificationPermission]);

  // Install PWA
  const installPWA = async () => {
    if (!deferredPrompt) return;

    try {
      const result = await deferredPrompt.prompt();
      
      if (result.outcome === 'accepted') {
        console.log('PWA installed successfully');
        setIsInstalled(true);
        setShowInstallBanner(false);
        
        // Track installation
        if (window.gtag) {
          window.gtag('event', 'pwa_install', {
            event_category: 'PWA',
            event_label: 'Install Success'
          });
        }
      } else {
        console.log('PWA installation dismissed');
        localStorage.setItem('pwa-install-dismissed', Date.now().toString());
      }
    } catch (error) {
      console.error('PWA installation failed:', error);
    }

    setDeferredPrompt(null);
  };

  // Dismiss install banner
  const dismissInstallBanner = () => {
    setShowInstallBanner(false);
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  };

  // Update PWA
  const updatePWA = () => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then((registration) => {
        if (registration && registration.waiting) {
          registration.waiting.postMessage({ type: 'SKIP_WAITING' });
          window.location.reload();
        }
      });
    }
  };

  // Request notification permission
  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      try {
        const permission = await Notification.requestPermission();
        setNotificationPermission(permission);
        
        if (permission === 'granted') {
          // Track permission granted
          if (window.gtag) {
            window.gtag('event', 'notification_permission_granted', {
              event_category: 'PWA',
              event_label: 'Notifications Enabled'
            });
          }
        }
      } catch (error) {
        console.error('Notification permission request failed:', error);
      }
    }
  };

  return (
    <>
      {/* Install Banner */}
      <AnimatePresence>
        {showInstallBanner && isInstallable && !isInstalled && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            className="fixed bottom-4 left-4 right-4 z-50 max-w-md mx-auto"
          >
            <div className="bg-gradient-to-r from-charcoal to-golden rounded-2xl shadow-2xl p-6 text-white">
              <div className="flex items-start gap-4">
                <div className="text-3xl">🧘‍♀️</div>
                <div className="flex-1">
                  <h3 className="font-semibold text-lg mb-2">
                    Zainstaluj aplikację BAKASANA
                  </h3>
                  <p className="text-sm opacity-90 mb-4">
                    Szybki dostęp do retreatów, galerii i rezerwacji. Działa offline!
                  </p>
                  <div className="flex gap-3">
                    <UnifiedButton
                      onClick={installPWA}
                      variant="secondary"
                      size="sm"
                      className="bg-white text-charcoal hover:bg-gray-100"
                    >
                      Zainstaluj
                    </UnifiedButton>
                    <UnifiedButton
                      onClick={dismissInstallBanner}
                      variant="ghost"
                      size="sm"
                      className="text-white hover:bg-white/20"
                    >
                      Później
                    </UnifiedButton>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Update Banner */}
      <AnimatePresence>
        {updateAvailable && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            className="fixed top-4 left-4 right-4 z-50 max-w-md mx-auto"
          >
            <div className="bg-blue-600 rounded-lg shadow-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold">Aktualizacja dostępna</h4>
                  <p className="text-sm opacity-90">
                    Nowa wersja aplikacji jest gotowa
                  </p>
                </div>
                <UnifiedButton
                  onClick={updatePWA}
                  variant="secondary"
                  size="sm"
                  className="bg-white text-blue-600 hover:bg-gray-100"
                >
                  Aktualizuj
                </UnifiedButton>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Offline Indicator */}
      <AnimatePresence>
        {!isOnline && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-0 left-0 right-0 z-50 bg-orange-500 text-white text-center py-2 text-sm"
          >
            📡 Tryb offline - niektóre funkcje mogą być niedostępne
          </motion.div>
        )}
      </AnimatePresence>

      {/* Notification Permission Request */}
      {isInstalled && notificationPermission === 'default' && (
        <div className="fixed bottom-20 left-4 right-4 z-40 max-w-sm mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-4 border">
            <div className="flex items-start gap-3">
              <div className="text-2xl">🔔</div>
              <div className="flex-1">
                <h4 className="font-semibold text-sm mb-1">
                  Włącz powiadomienia
                </h4>
                <p className="text-xs text-gray-600 mb-3">
                  Otrzymuj informacje o nowych retreatach
                </p>
                <div className="flex gap-2">
                  <UnifiedButton
                    onClick={requestNotificationPermission}
                    variant="primary"
                    size="sm"
                    className="text-xs"
                  >
                    Włącz
                  </UnifiedButton>
                  <UnifiedButton
                    onClick={() => setNotificationPermission('denied')}
                    variant="ghost"
                    size="sm"
                    className="text-xs"
                  >
                    Nie teraz
                  </UnifiedButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// Export PWA status hook
export function usePWAStatus() {
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  useEffect(() => {
    const checkStatus = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isIOSStandalone = window.navigator.standalone === true;
      setIsInstalled(isStandalone || isIOSStandalone);
      setIsOnline(navigator.onLine);
    };

    checkStatus();

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return { isInstalled, isOnline, updateAvailable };
}
