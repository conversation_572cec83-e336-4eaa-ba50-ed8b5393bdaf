import { NextResponse } from 'next/server';
import { subscriptions } from '../subscribe/route.js';

export async function POST(request) {
  try {
    const { subscription } = await request.json();

    if (!subscription || !subscription.endpoint) {
      return NextResponse.json(
        { error: 'Invalid subscription data' },
        { status: 400 }
      );
    }

    // Generate subscription ID from endpoint
    const subscriptionId = generateSubscriptionId(subscription.endpoint);
    
    // Find and remove subscription
    const existingSubscription = subscriptions.get(subscriptionId);
    
    if (existingSubscription) {
      // Mark as inactive instead of deleting (for analytics)
      existingSubscription.isActive = false;
      existingSubscription.unsubscribedAt = new Date().toISOString();
      
      console.log(`Push subscription unsubscribed: ${subscriptionId}`);
      console.log(`Active subscriptions: ${Array.from(subscriptions.values()).filter(s => s.isActive).length}`);

      return NextResponse.json({
        success: true,
        message: 'Successfully unsubscribed from push notifications'
      });
    } else {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('Push unsubscription error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to process unsubscription',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

// Generate unique subscription ID (same logic as subscribe)
function generateSubscriptionId(endpoint) {
  const url = new URL(endpoint);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 1];
  return id.substring(0, 16);
}
