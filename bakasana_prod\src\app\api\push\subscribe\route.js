import { NextResponse } from 'next/server';

// In a real application, you would store subscriptions in a database
// For this example, we'll use a simple in-memory store
const subscriptions = new Map();

export async function POST(request) {
  try {
    const { subscription, userAgent, timestamp } = await request.json();

    if (!subscription || !subscription.endpoint) {
      return NextResponse.json(
        { error: 'Invalid subscription data' },
        { status: 400 }
      );
    }

    // Generate a unique ID for the subscription
    const subscriptionId = generateSubscriptionId(subscription.endpoint);
    
    // Store subscription with metadata
    const subscriptionData = {
      id: subscriptionId,
      subscription: subscription,
      userAgent: userAgent,
      subscribedAt: timestamp || new Date().toISOString(),
      isActive: true,
      tags: ['all', 'retreats'] // Default tags
    };

    subscriptions.set(subscriptionId, subscriptionData);

    console.log(`New push subscription: ${subscriptionId}`);
    console.log(`Total subscriptions: ${subscriptions.size}`);

    // In a real application, you would:
    // 1. Store in database (MongoDB, PostgreSQL, etc.)
    // 2. Associate with user account if authenticated
    // 3. Set up subscription preferences
    // 4. Send welcome notification

    // Send welcome notification
    try {
      await sendWelcomeNotification(subscription);
    } catch (error) {
      console.error('Failed to send welcome notification:', error);
    }

    return NextResponse.json({
      success: true,
      subscriptionId: subscriptionId,
      message: 'Successfully subscribed to push notifications'
    });

  } catch (error) {
    console.error('Push subscription error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to process subscription',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

// Generate unique subscription ID
function generateSubscriptionId(endpoint) {
  const url = new URL(endpoint);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 1];
  return id.substring(0, 16); // Use first 16 characters
}

// Send welcome notification
async function sendWelcomeNotification(subscription) {
  const webpush = await import('web-push');
  
  // Configure web-push (you would set these in environment variables)
  webpush.setVapidDetails(
    'mailto:<EMAIL>',
    process.env.VAPID_PUBLIC_KEY,
    process.env.VAPID_PRIVATE_KEY
  );

  const payload = JSON.stringify({
    title: 'Witaj w BAKASANA! 🧘‍♀️',
    body: 'Dziękujemy za włączenie powiadomień. Będziemy informować Cię o nowych retreatach!',
    icon: '/apple-touch-icon.png',
    badge: '/favicon-32x32.png',
    image: '/images/og-image.jpg',
    data: {
      url: '/',
      type: 'welcome',
      timestamp: new Date().toISOString()
    },
    actions: [
      {
        action: 'explore',
        title: 'Przeglądaj retreaty',
        icon: '/favicon-16x16.png'
      }
    ],
    requireInteraction: false,
    silent: false
  });

  try {
    await webpush.sendNotification(subscription, payload);
    console.log('Welcome notification sent successfully');
  } catch (error) {
    console.error('Failed to send welcome notification:', error);
    throw error;
  }
}

// GET endpoint to retrieve subscription stats (for admin)
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('admin_key');

    // Simple admin authentication (use proper auth in production)
    if (adminKey !== process.env.ADMIN_API_KEY) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const stats = {
      totalSubscriptions: subscriptions.size,
      activeSubscriptions: Array.from(subscriptions.values()).filter(s => s.isActive).length,
      subscriptionsByDate: getSubscriptionsByDate(),
      recentSubscriptions: Array.from(subscriptions.values())
        .sort((a, b) => new Date(b.subscribedAt) - new Date(a.subscribedAt))
        .slice(0, 10)
        .map(s => ({
          id: s.id,
          subscribedAt: s.subscribedAt,
          userAgent: s.userAgent?.substring(0, 50) + '...',
          isActive: s.isActive
        }))
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error retrieving subscription stats:', error);
    
    return NextResponse.json(
      { error: 'Failed to retrieve stats' },
      { status: 500 }
    );
  }
}

// Helper function to get subscriptions by date
function getSubscriptionsByDate() {
  const dateGroups = {};
  
  Array.from(subscriptions.values()).forEach(subscription => {
    const date = new Date(subscription.subscribedAt).toISOString().split('T')[0];
    dateGroups[date] = (dateGroups[date] || 0) + 1;
  });

  return Object.entries(dateGroups)
    .sort(([a], [b]) => new Date(b) - new Date(a))
    .slice(0, 30) // Last 30 days
    .map(([date, count]) => ({ date, count }));
}

// Export subscriptions for use in other API routes
export { subscriptions };
